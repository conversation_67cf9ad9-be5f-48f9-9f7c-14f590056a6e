import React, { useEffect } from 'react';
import { View, Text } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Feather } from '@expo/vector-icons';
import { StyledView, StyledText, StyledScrollView } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';

export default function OffersScreen() {
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'Offers - CasaPay';
    }
  }, []);

  return (
    <View className="flex-1 bg-gray-50">
      <StatusBar style="dark" />
      
      <StyledView className="w-full max-w-md mx-auto flex-1">
        <StyledScrollView className="flex-grow px-6 pb-10">
          <StyledView className="items-center py-12">
            {/* Icon */}
            <StyledView className="w-20 h-20 bg-gray-100 rounded-full items-center justify-center mb-6">
              <Feather name="gift" size={32} color="#9CA3AF" />
            </StyledView>

            {/* Header */}
            <HeaderText className="text-2xl font-bold text-center mb-4">
              Exclusive Offers
            </HeaderText>
            <SubtitleText className="text-center mb-8">
              Discover special deals, discounts, and promotions from our partner network designed specifically for CasaPay tenants.
            </SubtitleText>

            {/* Coming Soon Card */}
            <StyledView className="bg-secondary/5 border border-secondary/15 p-6 rounded-xl w-full mb-6">
              <StyledView className="flex-row items-center mb-4">
                <Feather name="clock" size={20} color="#4ca2f5" />
                <StyledText className="text-secondary font-medium ml-2 text-lg">Coming Soon</StyledText>
              </StyledView>
              <StyledText className="text-gray-600 mb-4">
                We're working on bringing you amazing offers and deals from our trusted partners.
              </StyledText>
              
              {/* Feature List */}
              <StyledView className="space-y-3">
                <StyledView className="flex-row items-center">
                  <Feather name="percent" size={16} color="#4ca2f5" />
                  <StyledText className="text-gray-600 ml-3">Exclusive tenant discounts</StyledText>
                </StyledView>
                <StyledView className="flex-row items-center">
                  <Feather name="home" size={16} color="#4ca2f5" />
                  <StyledText className="text-gray-600 ml-3">Home & lifestyle deals</StyledText>
                </StyledView>
                <StyledView className="flex-row items-center">
                  <Feather name="shopping-bag" size={16} color="#4ca2f5" />
                  <StyledText className="text-gray-600 ml-3">Local business promotions</StyledText>
                </StyledView>
                <StyledView className="flex-row items-center">
                  <Feather name="credit-card" size={16} color="#4ca2f5" />
                  <StyledText className="text-gray-600 ml-3">Financial service benefits</StyledText>
                </StyledView>
                <StyledView className="flex-row items-center">
                  <Feather name="truck" size={16} color="#4ca2f5" />
                  <StyledText className="text-gray-600 ml-3">Moving & relocation services</StyledText>
                </StyledView>
              </StyledView>
            </StyledView>

            {/* Benefits Preview */}
            <StyledView className="bg-white border border-gray-200 p-6 rounded-xl w-full">
              <StyledView className="flex-row items-center mb-4">
                <Feather name="star" size={20} color="#fbbf24" />
                <StyledText className="font-medium text-gray-900 ml-2">What to Expect</StyledText>
              </StyledView>
              
              <StyledView className="space-y-4">
                <StyledView>
                  <StyledText className="font-medium text-gray-900 mb-1">Partner Network</StyledText>
                  <StyledText className="text-gray-600 text-sm">
                    Access to deals from furniture stores, utility providers, insurance companies, and local services.
                  </StyledText>
                </StyledView>
                
                <StyledView>
                  <StyledText className="font-medium text-gray-900 mb-1">Personalized Offers</StyledText>
                  <StyledText className="text-gray-600 text-sm">
                    Tailored recommendations based on your location, preferences, and rental history.
                  </StyledText>
                </StyledView>
                
                <StyledView>
                  <StyledText className="font-medium text-gray-900 mb-1">Easy Redemption</StyledText>
                  <StyledText className="text-gray-600 text-sm">
                    Simple one-tap redemption process with automatic discount codes and special links.
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>

            {/* Notification Signup */}
            <StyledView className="bg-gradient-to-r from-secondary/10 to-secondary/5 border border-secondary/20 p-6 rounded-xl w-full mt-6">
              <StyledView className="flex-row items-center mb-3">
                <Feather name="bell" size={20} color="#4ca2f5" />
                <StyledText className="text-secondary font-medium ml-2">Stay Updated</StyledText>
              </StyledView>
              <StyledText className="text-gray-600 mb-4">
                Be the first to know when exclusive offers become available. We'll notify you when new deals are added to your area.
              </StyledText>
              <StyledView className="flex-row items-center">
                <Feather name="check-circle" size={16} color="#10b981" />
                <StyledText className="text-green-600 ml-2 text-sm font-medium">
                  You'll be notified automatically
                </StyledText>
              </StyledView>
            </StyledView>
          </StyledView>
        </StyledScrollView>
      </StyledView>
    </View>
  );
}
