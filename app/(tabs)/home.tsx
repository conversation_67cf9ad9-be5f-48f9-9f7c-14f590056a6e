import React, { useEffect } from 'react';
import { View, Text } from 'react-native';

export default function HomeScreen() {
  console.log('🏠 HomeScreen is rendering!');

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'Home - CasaPay';
    }
  }, []);

  return (
    <View className="flex-1 bg-gray-50 items-center justify-center">
      <Text className="text-2xl font-bold text-gray-900 mb-4">🏠 Home Tab</Text>
      <Text className="text-gray-600">Tab navigation is working!</Text>
    </View>
  );
}