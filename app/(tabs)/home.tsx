import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { apiRequest } from '@/utils/apiUtils';
import { getStatusBgColor, getStatusTextColor, capitalizeFirstLetter } from '@/utils/statusUtils';
import type { DashboardPaymentAgreement, UpcomingPayment, PaymentAgreement } from '@/types';
import { formatCurrency } from '@/utils/i10n';

export default function HomeScreen() {
  const { state } = useAuth();
  const user = state.user;
  const token = state.token;

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'Home - CasaPay';
    }
  }, []);

  // State for API data
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [upcomingPayment, setUpcomingPayment] = useState<UpcomingPayment>();
  const [paymentAgreements, setPaymentAgreements] = useState<DashboardPaymentAgreement[]>([]);

  // Fetch dashboard data from API
  const fetchDashboardData = async () => {
    if (!token) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch payment agreements from the API
      try {
        // Fetch payment agreements
        const agreementsData = await apiRequest(
          '/api/v1/tenant/mcp',
          'POST',
          {
            jsonrpc: '2.0',
            method: 'tenant.get_payment_agreements',
            params: {}
          },
        );

        if (!agreementsData) {
          console.warn('Failed to fetch payment agreements: No data returned');
          throw new Error('Failed to fetch payment agreements');
        }

        // The actual structure is result.payment_agreements
        const agreements = agreementsData.result?.payment_agreements || [];

        if (!Array.isArray(agreements) || agreements.length === 0) {
          throw new Error('No payment agreements found');
        }

        // Fetch upcoming payment data
        const upcomingPaymentData = await apiRequest(
          '/api/v1/tenant/mcp',
          'POST',
          {
            jsonrpc: '2.0',
            method: 'tenant.get_upcoming_payment',
            params: {}
          },
        );
        if (upcomingPaymentData && upcomingPaymentData.result && upcomingPaymentData.result.upcoming_payment) {
          const payment = upcomingPaymentData.result.upcoming_payment;
          setUpcomingPayment({
            amount: payment.amount,
            dueDate: new Date(payment.due_date).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }),
            property: payment.property_name,
            payment_agreement: payment.payment_agreement
          });
        }

        // Map API data to our format
        const mappedAgreements = agreements.map((item: PaymentAgreement) => {
          // Extract property info if available
          const property = item.property || {};

          // Extract address components
          const addressParts = [
            property.street_address,
            property.city,
            property.state,
            property.postal_code,
            property.country
          ].filter(Boolean);

          // Format address
          const address = addressParts.join(', ');

          // Determine status
          let status = item.status || 'active';
          let pendingAction = null;
          let validUntil = null;
          let endDate = null;

          // Format the property title
          const propertyTitle = property.street_address ?
            `${property.street_address}` :
            item.agreement_id || 'Property';

          if (status === 'pending') {
            status = 'scoring';
            pendingAction = `Application submitted ${new Date(item.created_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
          } else if (status === 'signing') {
            pendingAction = 'Pending signature';
          } else if (status === 'active') {
            // Set valid until to 1 year from created_at if not specified
            const createdDate = new Date(item.created_at);
            const validUntilDate = new Date(createdDate);
            validUntilDate.setFullYear(validUntilDate.getFullYear() + 1);
            validUntil = validUntilDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
          } else if (status === 'closed' || status === 'terminated') {
            // Set end date to updated_at if not specified
            endDate = new Date(item.updated_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
          }

          return {
            id: item.id ? item.id.toString() : Math.random().toString(),
            title: propertyTitle,
            address: address || 'No address provided',
            amount: Number(item.base_monthly_rent),
            utilities: item.utilities_included || false,
            status: status,
            pendingAction: pendingAction,
            validUntil: validUntil,
            endDate: endDate,
            payment_agreement: item
          };
        });

        setPaymentAgreements(mappedAgreements);

      } catch (err) {
        console.error('Error fetching data from API:', err);
        setError('Failed to fetch data from the API. Please try again later.');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching data');
      console.error('Dashboard data fetch error:', err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, [token]);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDashboardData();
  };

  // Function to handle payment button click
  const handlePaymentClick = async () => {
      if(upcomingPayment?.payment_agreement.status === 'signing') {
        router.push(`/(dashboard)/payment-agreement/${upcomingPayment?.payment_agreement.id}/confirm`)
      }
      else {
        router.push(`/(dashboard)/payment-agreement/${upcomingPayment?.payment_agreement.id}/payment`)
      }
  };

  // Loading state
  if (loading) {
    return (
      <View className="flex-1 bg-white justify-center items-center">
        <StatusBar style="dark" />
        <ActivityIndicator size="large" color="#4ca2f5" />
        <Text className="mt-4 text-gray-500">Loading dashboard...</Text>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View className="flex-1 bg-white justify-center items-center p-4">
        <StatusBar style="dark" />
        <Text className="text-red-600 text-lg mb-2">Error</Text>
        <Text className="text-gray-500 text-center mb-6">{error}</Text>
        <TouchableOpacity
          className="bg-secondary rounded-xl py-3 px-6"
          onPress={() => fetchDashboardData()}
        >
          <Text className="text-white font-medium">Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50 items-center">
      <StatusBar style="dark" />

      <View className="w-full max-w-md mx-auto flex-1">
        <ScrollView
          className="flex-1 px-6 pb-10"
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          {/* Welcome Section */}
          <View className="py-6 px-4 bg-white rounded-xl mt-6 mb-4">
            <Text className="text-2xl font-semibold mb-3">Welcome back, {user?.first_name || 'Sarah'}!</Text>
            <Text className="text-gray-500 mb-4">Here's your rental payments overview</Text>

          {/* Upcoming Payment */}
          {upcomingPayment && (
            <View className="bg-secondary/5 border border-secondary/15 p-4 my-4 rounded-xl">
              <View className="flex-row justify-between items-center">
                <View className="flex-row items-center">
                  <View className="w-10 h-10 rounded-full bg-secondary/10 justify-center items-center mr-3">
                    <Text className="text-gray-600 text-lg">⏱️</Text>
                  </View>
                  <View>
                  <Text className="text-xs font-medium mb-2">Upcoming Payment</Text>
                    <Text className="text-base font-semibold">{formatCurrency(Number(upcomingPayment.amount), upcomingPayment.payment_agreement.currency)} due on {upcomingPayment.dueDate}</Text>
                    <Text className="text-gray-500 text-xs">{upcomingPayment.property}</Text>
                  </View>
                </View>
                <TouchableOpacity
                  className="bg-secondary rounded-xl py-2 px-4"
                  onPress={handlePaymentClick}
                >
                  <Text className="text-white font-medium">Pay Now</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
          </View>

          {/* Payment Agreements */}
          <View className="px-4 mt-4 mb-8 bg-white py-5 rounded-xl">
            <Text className="text-xl font-semibold mb-4">Payment Agreements</Text>

            {paymentAgreements.length > 0 ? (
              <View>
                {paymentAgreements.map(agreement => (
                  <TouchableOpacity
                    key={agreement.id}
                    onPress={() => router.push(`/(dashboard)/payment-agreement/${agreement.id}`)}
                    activeOpacity={0.7}
                  >
                    <View className="bg-white border border-gray-100 mb-4 rounded-xl overflow-hidden">
                      <View className="p-4">
                        <View className="flex-row justify-between items-start">
                          <View className="flex-1">
                            <View className="flex-row items-center gap-2 mb-1">
                              <Text className="font-medium text-base">{agreement.title}</Text>
                              {agreement.status && (
                       <View className="self-start rounded-full px-2 py-0.5" style={{ backgroundColor: getStatusBgColor(agreement.status) }}>
                       <Text className="text-xs" style={{ color: getStatusTextColor(agreement.status) }}>
                         {capitalizeFirstLetter(agreement.status)}
                       </Text>
                     </View>
                      )}
                            </View>
                            <Text className="text-gray-500 text-sm">{agreement.address}</Text>
                            <Text className="text-gray-500 text-xs mt-1">
                              {agreement.pendingAction ? agreement.pendingAction :
                               agreement.validUntil ? `Valid until ${agreement.validUntil}` :
                               agreement.endDate ? `Ended ${agreement.endDate}` : ''}
                            </Text>
                          </View>
                          <View>
                            <Text className="font-medium text-right">{formatCurrency(agreement.amount, agreement.payment_agreement.currency)}</Text>
                            <Text className="text-gray-400 text-right mt-1 text-xs">
                              {agreement.utilities ? 'Utilities included' : '+ utilities'}
                            </Text>
                          </View>
                        </View>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            ) : (
              <View className="items-center py-6 bg-white rounded-xl">
                <Text className="text-gray-500 text-center">No payment agreements found at the moment.</Text>
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </View>
  );
}
