import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { Feather } from '@expo/vector-icons';
import { StyledView, StyledText, StyledScrollView, StyledTouchableOpacity } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';
import { Tenant } from '@/types';

export default function ProfileScreen() {
  const { state, logout } = useAuth();
  const user = (state.user as unknown) as Tenant;

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'Profile - CasaPay';
    }
  }, []);

  const handleLogout = () => {
    Alert.alert(
      'Log Out',
      'Are you sure you want to log out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Log Out',
          style: 'destructive',
          onPress: logout,
        },
      ]
    );
  };

  const profileMenuItems = [
    {
      icon: 'user',
      title: 'Personal Information',
      subtitle: 'Update your profile details',
      onPress: () => router.push('/(dashboard)/profile'),
      color: '#4ca2f5',
    },
    {
      icon: 'star',
      title: 'Tenant Scorecard',
      subtitle: 'View your rental profile',
      onPress: () => {
        // Navigate to scorecard - we'll need to create this route
        console.log('Navigate to scorecard');
      },
      color: '#fbbf24',
    },
    {
      icon: 'credit-card',
      title: 'Payment Methods',
      subtitle: 'Manage your payment options',
      onPress: () => {
        // Navigate to payment methods - placeholder
        console.log('Navigate to payment methods');
      },
      color: '#10b981',
    },
    {
      icon: 'settings',
      title: 'Settings & Preferences',
      subtitle: 'App settings and notifications',
      onPress: () => {
        // Navigate to settings - placeholder
        console.log('Navigate to settings');
      },
      color: '#6b7280',
    },
    {
      icon: 'help-circle',
      title: 'Help & Support',
      subtitle: 'Get help and contact support',
      onPress: () => {
        // Navigate to help - placeholder
        console.log('Navigate to help');
      },
      color: '#8b5cf6',
    },
  ];

  return (
    <View className="flex-1 bg-gray-50">
      <StatusBar style="dark" />
      
      <StyledView className="w-full max-w-md mx-auto flex-1">
        <StyledScrollView className="flex-grow px-6 pb-10">
          {/* Profile Header */}
          <StyledView className="bg-white rounded-xl p-6 mt-6 mb-6">
            <StyledView className="items-center">
              {/* Avatar */}
              <StyledView className="w-20 h-20 rounded-full bg-secondary items-center justify-center mb-4">
                <StyledText className="text-white text-2xl font-semibold">
                  {user?.first_name?.charAt(0).toUpperCase() || 'U'}
                </StyledText>
              </StyledView>
              
              {/* User Info */}
              <HeaderText className="text-xl font-bold text-center mb-1">
                {`${user?.first_name || ''} ${user?.last_name || ''}`.trim() || 'User'}
              </HeaderText>
              <SubtitleText className="text-center mb-2">
                {user?.email || 'No email provided'}
              </SubtitleText>
              
              {/* CasaPay Status */}
              <StyledView className="flex-row items-center bg-secondary/10 px-3 py-1 rounded-full">
                <Feather name="shield" size={14} color="#4ca2f5" />
                <StyledText className="text-secondary font-medium ml-1 text-sm">
                  CasaPay Member
                </StyledText>
              </StyledView>
            </StyledView>
          </StyledView>

          {/* Menu Items */}
          <StyledView className="space-y-3">
            {profileMenuItems.map((item, index) => (
              <StyledTouchableOpacity
                key={index}
                className="bg-white rounded-xl p-4 flex-row items-center"
                onPress={item.onPress}
                variant="link"
              >
                <StyledView 
                  className="w-10 h-10 rounded-full items-center justify-center mr-4"
                  style={{ backgroundColor: `${item.color}15` }}
                >
                  <Feather name={item.icon as any} size={20} color={item.color} />
                </StyledView>
                
                <StyledView className="flex-1">
                  <StyledText className="font-medium text-gray-900 mb-1">
                    {item.title}
                  </StyledText>
                  <StyledText className="text-gray-500 text-sm">
                    {item.subtitle}
                  </StyledText>
                </StyledView>
                
                <Feather name="chevron-right" size={20} color="#9CA3AF" />
              </StyledTouchableOpacity>
            ))}
          </StyledView>

          {/* App Information */}
          <StyledView className="bg-white rounded-xl p-6 mt-6">
            <StyledText className="font-medium text-gray-900 mb-4">App Information</StyledText>
            
            <StyledView className="space-y-3">
              <StyledView className="flex-row justify-between items-center">
                <StyledText className="text-gray-600">Version</StyledText>
                <StyledText className="text-gray-900">1.0.0</StyledText>
              </StyledView>
              
              <StyledView className="flex-row justify-between items-center">
                <StyledText className="text-gray-600">Build</StyledText>
                <StyledText className="text-gray-900">Beta</StyledText>
              </StyledView>
              
              <TouchableOpacity
                onPress={() => {
                  // Navigate to privacy policy
                  console.log('Navigate to privacy policy');
                }}
              >
                <StyledText className="text-secondary">Privacy Policy</StyledText>
              </TouchableOpacity>
              
              <TouchableOpacity
                onPress={() => {
                  // Navigate to terms of service
                  console.log('Navigate to terms of service');
                }}
              >
                <StyledText className="text-secondary">Terms of Service</StyledText>
              </TouchableOpacity>
            </StyledView>
          </StyledView>

          {/* Logout Button */}
          <StyledTouchableOpacity
            className="bg-white border border-red-200 rounded-xl p-4 flex-row items-center justify-center mt-6"
            onPress={handleLogout}
            variant="link"
          >
            <Feather name="log-out" size={20} color="#ef4444" />
            <StyledText className="text-red-600 font-medium ml-2">Log Out</StyledText>
          </StyledTouchableOpacity>
        </StyledScrollView>
      </StyledView>
    </View>
  );
}
