import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Feather } from '@expo/vector-icons';
import { StyledView, StyledText, StyledScrollView, StyledTouchableOpacity } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';

// Rental Operator Messages Screen
function RentalOperatorScreen() {
  return (
    <StyledScrollView className="flex-grow px-6 pb-10">
      <StyledView className="items-center py-12">
        <StyledView className="w-16 h-16 bg-gray-100 rounded-full items-center justify-center mb-6">
          <Feather name="home" size={24} color="#9CA3AF" />
        </StyledView>
        <HeaderText className="text-xl font-bold text-center mb-4">
          Rental Operator Messages
        </HeaderText>
        <SubtitleText className="text-center mb-8">
          Communicate directly with your property manager or landlord about maintenance, lease questions, and property matters.
        </SubtitleText>

        {/* Coming Soon Message */}
        <StyledView className="bg-secondary/5 border border-secondary/15 p-6 rounded-xl w-full">
          <StyledView className="flex-row items-center mb-3">
            <Feather name="clock" size={20} color="#4ca2f5" />
            <StyledText className="text-secondary font-medium ml-2">Coming Soon</StyledText>
          </StyledView>
          <StyledText className="text-gray-600">
            Direct messaging with your rental operator will be available soon. You'll be able to:
          </StyledText>
          <StyledView className="mt-4 space-y-2">
            <StyledView className="flex-row items-center">
              <Feather name="check" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-2">Report maintenance issues</StyledText>
            </StyledView>
            <StyledView className="flex-row items-center">
              <Feather name="check" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-2">Ask lease-related questions</StyledText>
            </StyledView>
            <StyledView className="flex-row items-center">
              <Feather name="check" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-2">Schedule property visits</StyledText>
            </StyledView>
          </StyledView>
        </StyledView>
      </StyledView>
    </StyledScrollView>
  );
}

// Co-tenants Messages Screen
function CoTenantsScreen() {
  return (
    <StyledScrollView className="flex-grow px-6 pb-10">
      <StyledView className="items-center py-12">
        <StyledView className="w-16 h-16 bg-gray-100 rounded-full items-center justify-center mb-6">
          <Feather name="users" size={24} color="#9CA3AF" />
        </StyledView>
        <HeaderText className="text-xl font-bold text-center mb-4">
          Co-tenant Messages
        </HeaderText>
        <SubtitleText className="text-center mb-8">
          Connect with your roommates and co-tenants to coordinate household matters, shared expenses, and community updates.
        </SubtitleText>

        {/* Coming Soon Message */}
        <StyledView className="bg-secondary/5 border border-secondary/15 p-6 rounded-xl w-full">
          <StyledView className="flex-row items-center mb-3">
            <Feather name="clock" size={20} color="#4ca2f5" />
            <StyledText className="text-secondary font-medium ml-2">Coming Soon</StyledText>
          </StyledView>
          <StyledText className="text-gray-600">
            Co-tenant messaging will be available soon. You'll be able to:
          </StyledText>
          <StyledView className="mt-4 space-y-2">
            <StyledView className="flex-row items-center">
              <Feather name="check" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-2">Coordinate shared expenses</StyledText>
            </StyledView>
            <StyledView className="flex-row items-center">
              <Feather name="check" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-2">Plan household activities</StyledText>
            </StyledView>
            <StyledView className="flex-row items-center">
              <Feather name="check" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-2">Share important updates</StyledText>
            </StyledView>
          </StyledView>
        </StyledView>
      </StyledView>
    </StyledScrollView>
  );
}

// CasaPay Support Messages Screen
function CasaPaySupportScreen() {
  return (
    <StyledScrollView className="flex-grow px-6 pb-10">
      <StyledView className="items-center py-12">
        <StyledView className="w-16 h-16 bg-gray-100 rounded-full items-center justify-center mb-6">
          <Feather name="help-circle" size={24} color="#9CA3AF" />
        </StyledView>
        <HeaderText className="text-xl font-bold text-center mb-4">
          CasaPay Support
        </HeaderText>
        <SubtitleText className="text-center mb-8">
          Get help with your CasaPay account, payments, technical issues, and general questions from our support team.
        </SubtitleText>

        {/* Coming Soon Message */}
        <StyledView className="bg-secondary/5 border border-secondary/15 p-6 rounded-xl w-full">
          <StyledView className="flex-row items-center mb-3">
            <Feather name="clock" size={20} color="#4ca2f5" />
            <StyledText className="text-secondary font-medium ml-2">Coming Soon</StyledText>
          </StyledView>
          <StyledText className="text-gray-600">
            In-app support messaging will be available soon. You'll be able to:
          </StyledText>
          <StyledView className="mt-4 space-y-2">
            <StyledView className="flex-row items-center">
              <Feather name="check" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-2">Get payment assistance</StyledText>
            </StyledView>
            <StyledView className="flex-row items-center">
              <Feather name="check" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-2">Report technical issues</StyledText>
            </StyledView>
            <StyledView className="flex-row items-center">
              <Feather name="check" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-2">Ask account questions</StyledText>
            </StyledView>
          </StyledView>
        </StyledView>

        {/* Contact Information */}
        <StyledView className="bg-white border border-gray-200 p-6 rounded-xl w-full mt-6">
          <StyledText className="font-medium text-gray-900 mb-4">Need immediate help?</StyledText>
          <StyledView className="space-y-3">
            <StyledView className="flex-row items-center">
              <Feather name="mail" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-3"><EMAIL></StyledText>
            </StyledView>
            <StyledView className="flex-row items-center">
              <Feather name="phone" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-3">+****************</StyledText>
            </StyledView>
            <StyledView className="flex-row items-center">
              <Feather name="clock" size={16} color="#4ca2f5" />
              <StyledText className="text-gray-600 ml-3">Mon-Fri, 9AM-6PM EST</StyledText>
            </StyledView>
          </StyledView>
        </StyledView>
      </StyledView>
    </StyledScrollView>
  );
}

// Main Messages Screen with Sub-Navigation
export default function MessagesScreen() {
  const [activeTab, setActiveTab] = useState('rental-operator');

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'Messages - CasaPay';
    }
  }, []);

  const tabs = [
    { id: 'rental-operator', label: 'Landlord', component: RentalOperatorScreen },
    { id: 'co-tenants', label: 'Co-tenants', component: CoTenantsScreen },
    { id: 'casapay-support', label: 'Support', component: CasaPaySupportScreen },
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || RentalOperatorScreen;

  return (
    <View className="flex-1 bg-gray-50">
      <StatusBar style="dark" />

      {/* Custom Tab Bar */}
      <StyledView className="bg-white border-b border-gray-200">
        <StyledView className="flex-row">
          {tabs.map((tab) => (
            <StyledTouchableOpacity
              key={tab.id}
              className={`flex-1 py-4 items-center border-b-2 ${
                activeTab === tab.id
                  ? 'border-secondary'
                  : 'border-transparent'
              }`}
              onPress={() => setActiveTab(tab.id)}
              variant="link"
            >
              <StyledText className={`text-sm font-medium ${
                activeTab === tab.id
                  ? 'text-secondary'
                  : 'text-gray-500'
              }`}>
                {tab.label}
              </StyledText>
            </StyledTouchableOpacity>
          ))}
        </StyledView>
      </StyledView>

      {/* Tab Content */}
      <ActiveComponent />
    </View>
  );
}
