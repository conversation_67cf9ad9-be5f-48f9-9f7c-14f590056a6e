import React from 'react';
import { Platform } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
// Note: material-top-tabs might not be installed yet, using a fallback
// import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { Feather } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

// Import tab screens
import HomeScreen from './home';
import MessagesScreen from './messages';
import OffersScreen from './offers';
import ProfileScreen from './profile';

const BottomTab = createBottomTabNavigator();
// const TopTab = createMaterialTopTabNavigator(); // Commented out for now

// Tab configuration with icons and labels
const tabConfig = [
  {
    name: 'home',
    component: HomeScreen,
    label: 'Home',
    icon: 'home' as const,
  },
  {
    name: 'messages',
    component: MessagesScreen,
    label: 'Messages',
    icon: 'message-circle' as const,
  },
  {
    name: 'offers',
    component: OffersScreen,
    label: 'Offers',
    icon: 'tag' as const,
  },
  {
    name: 'profile',
    component: ProfileScreen,
    label: 'Profile',
    icon: 'user' as const,
  },
];

// Tab Navigator with Platform-Specific Positioning
function TabNavigator() {
  return (
    <SafeAreaView style={{ flex: 1 }} edges={Platform.OS === 'web' ? [] : ['top']}>
      <BottomTab.Navigator
        screenOptions={({ route }) => ({
          headerShown: false,
          tabBarIcon: ({ focused, color, size }) => {
            const tabInfo = tabConfig.find(tab => tab.name === route.name);
            const iconName = tabInfo?.icon || 'home';

            return (
              <Feather
                name={iconName}
                size={size || 24}
                color={color}
              />
            );
          },
          tabBarActiveTintColor: '#4ca2f5', // CasaPay secondary color
          tabBarInactiveTintColor: '#9CA3AF', // Gray-400
          tabBarStyle: {
            backgroundColor: 'white',
            borderTopWidth: Platform.OS === 'web' ? 0 : 1,
            borderBottomWidth: Platform.OS === 'web' ? 1 : 0,
            borderTopColor: '#E5E7EB', // Gray-200
            borderBottomColor: '#E5E7EB', // Gray-200
            paddingBottom: Platform.OS === 'ios' ? 20 : Platform.OS === 'web' ? 0 : 10,
            paddingTop: Platform.OS === 'web' ? 10 : 10,
            height: Platform.OS === 'ios' ? 90 : Platform.OS === 'web' ? 60 : 70,
            position: Platform.OS === 'web' ? 'relative' : 'absolute',
            bottom: Platform.OS === 'web' ? undefined : 0,
            top: Platform.OS === 'web' ? 0 : undefined,
          },
          tabBarLabelStyle: {
            fontSize: 12,
            fontWeight: '500',
            marginTop: 4,
          },
          tabBarItemStyle: {
            paddingVertical: 5,
          },
        })}
      >
        {tabConfig.map((tab) => (
          <BottomTab.Screen
            key={tab.name}
            name={tab.name}
            component={tab.component}
            options={{
              tabBarLabel: tab.label,
            }}
          />
        ))}
      </BottomTab.Navigator>
    </SafeAreaView>
  );
}

// Main Tab Layout
export default function TabLayout() {
  return <TabNavigator />;
}
