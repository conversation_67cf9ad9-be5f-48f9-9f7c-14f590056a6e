import React from 'react';
import { Platform, View } from 'react-native';
import { Tabs } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import TopNavigationHeader from '@/components/TopNavigationHeader';

export default function TabLayout() {
  console.log('🔥 TabLayout is rendering!');

  // Platform-specific tab bar configuration
  const isWeb = Platform.OS === 'web';

  return (
    <View style={{ flex: 1 }}>
      {/* Top Navigation Header for Web */}
      <TopNavigationHeader />

      <Tabs
        screenOptions={{
          headerShown: false,
          tabBarActiveTintColor: '#4ca2f5', // CasaPay secondary color
          tabBarInactiveTintColor: '#9CA3AF', // Gray-400
          tabBarStyle: {
            backgroundColor: 'white',
            borderTopWidth: isWeb ? 0 : 1,
            borderBottomWidth: isWeb ? 1 : 0,
            borderTopColor: '#E5E7EB', // Gray-200
            borderBottomColor: '#E5E7EB', // Gray-200
            paddingBottom: Platform.OS === 'ios' ? 20 : isWeb ? 0 : 10,
            paddingTop: 10,
            height: Platform.OS === 'ios' ? 90 : isWeb ? 60 : 70,
            position: isWeb ? 'relative' : 'absolute',
            bottom: isWeb ? undefined : 0,
            top: isWeb ? 0 : undefined,
            left: 0,
            right: 0,
            zIndex: 1000,
            display: isWeb ? 'none' : 'flex', // Hide tab bar on web since we have top nav
          },
          tabBarLabelStyle: {
            fontSize: 12,
            fontWeight: '500',
            marginTop: 4,
          },
          tabBarItemStyle: {
            paddingVertical: 5,
          },
        }}
      >
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, size }) => (
            <Feather name="home" size={size || 24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="messages"
        options={{
          title: 'Messages',
          tabBarIcon: ({ color, size }) => (
            <Feather name="message-circle" size={size || 24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="offers"
        options={{
          title: 'Offers',
          tabBarIcon: ({ color, size }) => (
            <Feather name="tag" size={size || 24} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <Feather name="user" size={size || 24} color={color} />
          ),
        }}
      />
    </Tabs>
    </View>
  );
}
