import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import WelcomeStep from '../../components/welcome/WelcomeStep';
import { ProgressBar } from '../../components/welcome/ProgressBar';
import { StyledView } from '../../components/ui/StyledComponents';

// Welcome steps data without text
const WELCOME_STEPS_DATA = [
  {
    id: '1',
    imageSource: require('../../assets/images/welcome-step-1.png'),
  },
  {
    id: '2',
    imageSource: require('../../assets/images/welcome-step-2.png'),
  },
  {
    id: '3',
    imageSource: require('../../assets/images/welcome-step-3.png'),
  },
];

export default function WelcomeIndexWeb() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(0);
  const [forceComplete, setForceComplete] = useState(false);
  const autoAdvanceTimer = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleGetStarted = async () => {
    // Skip directly to the final page (index 2) when user clicks "Get started"
    // This respects user intent to proceed immediately rather than viewing more content
    if (currentPage < WELCOME_STEPS_DATA.length - 1) {
      // Clear any existing timer
      if (autoAdvanceTimer.current) {
        clearTimeout(autoAdvanceTimer.current);
      }

      // Set all progress bars to completed state
      setForceComplete(true);

      const finalPage = WELCOME_STEPS_DATA.length - 1; // Jump to last page (index 2)
      goToPage(finalPage);
      return;
    }

    // If on the last step, this shouldn't be called (but handle it just in case)
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleLogin = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleRegister = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/register');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/register');
    }
  };

  const goToPage = (pageIndex: number) => {
    setCurrentPage(pageIndex);

    // Reset forceComplete when manually navigating
    if (forceComplete) {
      setForceComplete(false);
    }

    startAutoAdvance(pageIndex);
  };

  const onStepComplete = (stepIndex: number) => {
    // Auto-advance when progress bar completes
    const nextPage = stepIndex + 1;
    if (nextPage < WELCOME_STEPS_DATA.length) {
      goToPage(nextPage);
    }
  };

  const startAutoAdvance = (pageIndex: number) => {
    // Clear existing timer
    if (autoAdvanceTimer.current) {
      clearTimeout(autoAdvanceTimer.current);
    }

    // Don't auto-advance on the last page
    if (pageIndex >= WELCOME_STEPS_DATA.length - 1) {
      return;
    }

    // Auto-advance to next page after 3 seconds
    autoAdvanceTimer.current = setTimeout(() => {
      const nextPage = pageIndex + 1;
      if (nextPage < WELCOME_STEPS_DATA.length) {
        goToPage(nextPage);
      }
    }, 3000);
  };

  useEffect(() => {
    // Start the first page auto-advance
    startAutoAdvance(0);

    return () => {
      if (autoAdvanceTimer.current) {
        clearTimeout(autoAdvanceTimer.current);
      }
    };
  }, []);

  const currentStep = WELCOME_STEPS_DATA[currentPage];
  const isLastStep = currentPage === WELCOME_STEPS_DATA.length - 1;

  return (
    <StyledView className="flex-1 bg-white items-center justify-center min-h-screen">
      <StyledView className="w-full max-w-md mx-auto flex-1 px-6 py-4">
        <ProgressBar
          totalSteps={WELCOME_STEPS_DATA.length}
          currentStep={currentPage}
          stepDuration={3000}
          onStepComplete={onStepComplete}
          forceComplete={forceComplete}
        />
        <StyledView className="flex-1 justify-start pt-2">
          <WelcomeStep
            imageSource={currentStep.imageSource}
            onGetStarted={!isLastStep ? handleGetStarted : undefined}
            onLogin={isLastStep ? handleLogin : undefined}
            onRegister={isLastStep ? handleRegister : undefined}
            showDualButtons={isLastStep}
          />
        </StyledView>
      </StyledView>
    </StyledView>
  );
}
