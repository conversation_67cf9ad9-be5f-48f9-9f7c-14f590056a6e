import React, { useRef, useState, useEffect } from 'react';
import { View, SafeAreaView } from 'react-native';
import PagerView from 'react-native-pager-view';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import WelcomeStep from '../../components/welcome/WelcomeStep';
import { ProgressBar } from '../../components/welcome/ProgressBar';

// Welcome steps data without text
const WELCOME_STEPS_DATA = [
  {
    id: '1',
    imageSource: require('../../assets/images/welcome-step-1.png'),
  },
  {
    id: '2',
    imageSource: require('../../assets/images/welcome-step-2.png'),
  },
  {
    id: '3',
    imageSource: require('../../assets/images/welcome-step-3.png'),
  },
];

export default function WelcomeScreenNative() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(0);
  const [forceComplete, setForceComplete] = useState(false);
  const pagerRef = useRef<PagerView>(null);
  const autoAdvanceTimer = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleGetStarted = async () => {
    // Skip directly to the final page (index 2) when user clicks "Get started"
    // This respects user intent to proceed immediately rather than viewing more content
    if (currentPage < WELCOME_STEPS_DATA.length - 1) {
      // Clear any existing timer
      if (autoAdvanceTimer.current) {
        clearTimeout(autoAdvanceTimer.current);
      }

      // Set all progress bars to completed state
      setForceComplete(true);

      const finalPage = WELCOME_STEPS_DATA.length - 1; // Jump to last page (index 2)
      pagerRef.current?.setPage(finalPage);
      return;
    }

    // If on the last step, this shouldn't be called (but handle it just in case)
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleLogin = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleRegister = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/register');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/register');
    }
  };

  const onPageSelected = (e: any) => {
    const newPage = e.nativeEvent.position;
    setCurrentPage(newPage);

    // Reset forceComplete when manually navigating
    if (forceComplete) {
      setForceComplete(false);
    }

    startAutoAdvance(newPage);
  };

  const startAutoAdvance = (pageIndex: number) => {
    // Clear existing timer
    if (autoAdvanceTimer.current) {
      clearTimeout(autoAdvanceTimer.current);
    }

    // Don't auto-advance on the last page
    if (pageIndex >= WELCOME_STEPS_DATA.length - 1) {
      return;
    }

    // Auto-advance to next page after 3 seconds
    autoAdvanceTimer.current = setTimeout(() => {
      const nextPage = pageIndex + 1;
      if (nextPage < WELCOME_STEPS_DATA.length) {
        pagerRef.current?.setPage(nextPage);
      }
    }, 3000);
  };

  const onStepComplete = (stepIndex: number) => {
    // Auto-advance when progress bar completes
    const nextPage = stepIndex + 1;
    if (nextPage < WELCOME_STEPS_DATA.length) {
      pagerRef.current?.setPage(nextPage);
    }
  };

  useEffect(() => {
    // Start the first page auto-advance
    startAutoAdvance(0);

    return () => {
      if (autoAdvanceTimer.current) {
        clearTimeout(autoAdvanceTimer.current);
      }
    };
  }, []);

  const isLastStep = currentPage === WELCOME_STEPS_DATA.length - 1;

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="px-6 py-2">
        <ProgressBar
          totalSteps={WELCOME_STEPS_DATA.length}
          currentStep={currentPage}
          stepDuration={3000}
          onStepComplete={onStepComplete}
          forceComplete={forceComplete}
        />
      </View>
      <PagerView
        ref={pagerRef}
        style={{ flex: 1 }}
        initialPage={0}
        onPageSelected={onPageSelected}
      >
        {WELCOME_STEPS_DATA.map((step) => (
          <View key={step.id} className="flex-1">
            <WelcomeStep
              imageSource={step.imageSource}
              onGetStarted={!isLastStep ? handleGetStarted : undefined}
              onLogin={isLastStep ? handleLogin : undefined}
              onRegister={isLastStep ? handleRegister : undefined}
              showDualButtons={isLastStep}
            />
          </View>
        ))}
      </PagerView>
    </SafeAreaView>
  );
}
