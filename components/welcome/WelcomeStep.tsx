import React from 'react';
import { Image, Platform, ViewStyle } from 'react-native';
import { StyledText, StyledView, StyledTouchableOpacity } from '../ui/StyledComponents';

interface WelcomeStepProps {
  imageSource: any; // Adjust type as per your image source (e.g., ImageSourcePropType)
  onGetStarted?: () => void;
  onLogin?: () => void;
  onRegister?: () => void;
  showDualButtons?: boolean;
}

const WelcomeStep: React.FC<WelcomeStepProps> = ({
  imageSource,
  onGetStarted,
  onLogin,
  onRegister,
  showDualButtons = false,
}) => {
  const isWeb = Platform.OS === 'web';

  // Vertical stacked button layout - eliminates iOS width expansion issues
  // Both buttons use identical styling for consistency across platforms
  const getVerticalButtonStyles = () => {
    return {
      container: {
        width: '100%' as const,
        alignItems: 'center' as const,
      } as ViewStyle,
      button: {
        width: '80%' as const,
        minWidth: 200,
      } as ViewStyle,
    };
  };

  // Render buttons component
  const renderButtons = () => {
    if (showDualButtons) {
      const buttonStyles = getVerticalButtonStyles();

      return (
        <StyledView style={buttonStyles.container}>
          {/* Register button on top */}
          <StyledTouchableOpacity
            onPress={onRegister}
            className="bg-secondary hover:bg-secondary/90 rounded-xl py-4 px-6 items-center shadow-md transition-colors mb-4"
            style={buttonStyles.button}
          >
            <StyledText className="text-white text-base font-semibold">Register</StyledText>
          </StyledTouchableOpacity>

          {/* Log in button below */}
          <StyledTouchableOpacity
            onPress={onLogin}
            className="bg-white rounded-xl py-4 px-6 items-center shadow-md transition-colors hover:bg-gray-50 border-2 border-secondary"
            style={buttonStyles.button}
          >
            <StyledText className="text-secondary text-base font-semibold">Log in</StyledText>
          </StyledTouchableOpacity>
        </StyledView>
      );
    } else {
      return (
        <StyledView className="w-full items-center">
          <StyledTouchableOpacity
            onPress={onGetStarted}
            className="bg-secondary hover:bg-secondary/90 rounded-xl py-4 px-6 items-center shadow-md transition-colors"
            style={{ width: '80%', minWidth: 200 }}
          >
            <StyledText className="text-white text-base font-semibold">Get started</StyledText>
          </StyledTouchableOpacity>
        </StyledView>
      );
    }
  };

  // Web layout: Keep current centered positioning
  if (isWeb) {
    return (
      <StyledView className="w-full flex-1 items-center justify-start px-4 bg-white">
        <Image
          source={imageSource}
          className="w-full mb-6"
          style={{ height: 420 }}
          resizeMode="contain"
        />
        <StyledView className="mt-6 w-full items-center">
          {renderButtons()}
        </StyledView>
      </StyledView>
    );
  }

  // Mobile layout: Position buttons at bottom
  return (
    <StyledView className="w-full flex-1 bg-white">
      {/* Content area with image */}
      <StyledView className="flex-1 items-center justify-center px-4">
        <Image
          source={imageSource}
          className="w-full"
          style={{ height: 380 }}
          resizeMode="contain"
        />
      </StyledView>

      {/* Bottom button area for mobile - positioned in thumb-friendly zone */}
      <StyledView className="px-6 pb-6 pt-4 items-center" style={{ paddingBottom: 24 }}>
        {renderButtons()}
      </StyledView>
    </StyledView>
  );
};

export default WelcomeStep;
