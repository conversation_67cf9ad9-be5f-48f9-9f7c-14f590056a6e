import React, { useEffect, useRef } from 'react';
import { View, Animated, Platform } from 'react-native';
import { StyledView } from '../ui/StyledComponents';

interface ProgressBarProps {
  /** Total number of steps/bars */
  totalSteps: number;
  /** Current active step (0-based index) */
  currentStep: number;
  /** Duration in milliseconds for each step animation */
  stepDuration: number;
  /** Whether the progress should be paused */
  isPaused?: boolean;
  /** Callback when a step animation completes */
  onStepComplete?: (stepIndex: number) => void;
  /** Force all bars to completed state (used when skipping to final page) */
  forceComplete?: boolean;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  totalSteps,
  currentStep,
  stepDuration,
  isPaused = false,
  onStepComplete,
  forceComplete = false,
}) => {
  // Create animated values for each step (for native)
  const progressValues = useRef(
    Array.from({ length: totalSteps }, () => new Animated.Value(0))
  ).current;

  // Track current animation (for native)
  const currentAnimation = useRef<Animated.CompositeAnimation | null>(null);

  // Web-specific state for progress tracking
  const [webProgressStates, setWebProgressStates] = React.useState<number[]>(
    Array(totalSteps).fill(0)
  );

  // Native animation effect
  useEffect(() => {
    if (Platform.OS !== 'web') {
      if (forceComplete) {
        // Immediately set all bars to completed state
        progressValues.forEach((value, index) => {
          value.setValue(index <= currentStep ? 1 : 0);
        });
        return;
      }

      // Stop any existing animation
      if (currentAnimation.current) {
        currentAnimation.current.stop();
      }

      // Set previous steps to completed (100%)
      progressValues.forEach((value, index) => {
        if (index < currentStep) {
          value.setValue(1);
        } else if (index > currentStep) {
          value.setValue(0);
        }
      });

      // Animate current step if not paused
      if (!isPaused && currentStep < totalSteps) {
        const currentProgressValue = progressValues[currentStep];
        currentProgressValue.setValue(0);

        currentAnimation.current = Animated.timing(currentProgressValue, {
          toValue: 1,
          duration: stepDuration,
          useNativeDriver: false,
        });

        currentAnimation.current.start((finished) => {
          if (finished && onStepComplete) {
            onStepComplete(currentStep);
          }
        });
      }

      return () => {
        if (currentAnimation.current) {
          currentAnimation.current.stop();
        }
      };
    }
  }, [currentStep, stepDuration, isPaused, forceComplete, totalSteps, onStepComplete]);

  // Web animation effect
  useEffect(() => {
    if (Platform.OS === 'web') {
      // First, set the initial states (completed steps at 100%, others at 0%)
      const initialStates = Array(totalSteps).fill(0).map((_, index) => {
        if (forceComplete) {
          return index <= currentStep ? 100 : 0;
        }
        if (index < currentStep) {
          return 100; // Completed steps
        }
        return 0; // Current and future steps start at 0%
      });

      setWebProgressStates(initialStates);

      // Then, if we have an active step that should animate, trigger the animation
      if (!isPaused && currentStep < totalSteps && !forceComplete) {
        // Use a small delay to ensure the initial state is rendered before animation starts
        const animationTimer = setTimeout(() => {
          setWebProgressStates(prevStates => {
            const newStates = [...prevStates];
            newStates[currentStep] = 100; // Animate current step to 100%
            return newStates;
          });
        }, 50); // Small delay to ensure initial render

        // Set up completion callback
        const completionTimer = setTimeout(() => {
          if (onStepComplete) {
            onStepComplete(currentStep);
          }
        }, stepDuration + 50); // Add the initial delay

        return () => {
          clearTimeout(animationTimer);
          clearTimeout(completionTimer);
        };
      }
    }
  }, [currentStep, forceComplete, isPaused, totalSteps, stepDuration, onStepComplete]);

  const renderProgressBars = () => {
    return (
      <StyledView className="flex-row justify-center items-center mb-4">
        {Array.from({ length: totalSteps }).map((_, index) => (
          <StyledView key={index} className="mx-1">
            {/* Background bar */}
            <StyledView
              className="bg-gray-200 rounded-sm"
              style={{
                width: 60, // Compact width for mobile
                height: 3,
              }}
            >
              {/* Animated progress fill */}
              <Animated.View
                style={{
                  width: '100%',
                  height: '100%',
                  backgroundColor: '#4ca2f5', // CasaPay secondary color
                  borderRadius: 1,
                  transform: [
                    {
                      scaleX: progressValues[index],
                    },
                  ],
                  transformOrigin: 'left',
                }}
              />
            </StyledView>
          </StyledView>
        ))}
      </StyledView>
    );
  };

  // For web, we need to use CSS transitions instead of React Native Animated
  if (Platform.OS === 'web') {
    return (
      <StyledView className="flex-row justify-center items-center mb-4">
        {Array.from({ length: totalSteps }).map((_, index) => {
          const isCompleted = forceComplete ? index <= currentStep : index < currentStep;
          const isActive = !forceComplete && index === currentStep;
          const targetWidth = webProgressStates[index];

          return (
            <StyledView key={index} className="mx-1">
              <StyledView
                className="bg-gray-200 rounded-sm relative overflow-hidden"
                style={{
                  width: 60,
                  height: 3,
                }}
              >
                <StyledView
                  className="absolute top-0 left-0 h-full rounded-sm"
                  style={{
                    backgroundColor: '#4ca2f5',
                    width: `${targetWidth}%`,
                    transition: isActive && !isPaused
                      ? `width ${stepDuration}ms linear`
                      : isCompleted
                        ? 'none'
                        : 'width 0.3s ease',
                    transformOrigin: 'left',
                  }}
                />
              </StyledView>
            </StyledView>
          );
        })}
      </StyledView>
    );
  }

  return renderProgressBars();
};
