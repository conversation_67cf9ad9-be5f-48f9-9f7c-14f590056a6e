import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Platform } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { router, usePathname } from 'expo-router';
import { Feather } from '@expo/vector-icons';

interface TopNavigationHeaderProps {
  currentTab?: string;
}

export default function TopNavigationHeader({ currentTab }: TopNavigationHeaderProps) {
  const { state, logout } = useAuth();
  const user = state.user;
  const pathname = usePathname();
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const dropdownRef = useRef<View>(null);

  // Only show on web platform
  if (Platform.OS !== 'web') {
    return null;
  }

  // Close dropdown when clicking outside (web only)
  useEffect(() => {
    if (Platform.OS === 'web' && showProfileMenu) {
      const handleClickOutside = (event: any) => {
        if (dropdownRef.current && !dropdownRef.current.contains?.(event.target)) {
          setShowProfileMenu(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showProfileMenu]);

  // Navigation items
  const navItems = [
    { key: 'home', label: 'Dashboard', route: '/(tabs)/home', icon: 'home' },
    { key: 'messages', label: 'Messages', route: '/(tabs)/messages', icon: 'message-circle' },
    { key: 'offers', label: 'Offers', route: '/(tabs)/offers', icon: 'tag' },
    { key: 'profile', label: 'Profile', route: '/(tabs)/profile', icon: 'user' },
  ];

  const handleNavigation = (route: string) => {
    router.push(route as any);
  };

  const handleLogout = async () => {
    setShowProfileMenu(false);
    await logout();
  };

  const getActiveTab = () => {
    if (pathname.includes('/home')) return 'home';
    if (pathname.includes('/messages')) return 'messages';
    if (pathname.includes('/offers')) return 'offers';
    if (pathname.includes('/profile')) return 'profile';
    return 'home';
  };

  const activeTab = getActiveTab();

  return (
    <View className="bg-white border-b border-gray-200 shadow-sm">
      <View className="max-w-7xl mx-auto px-6">
        <View className="flex-row items-center justify-between h-16">
          {/* Logo */}
          <View className="flex-row items-center">
            <Text className="text-2xl font-bold text-gray-900">CasaPay</Text>
          </View>

          {/* Navigation Items */}
          <View className="flex-row items-center">
            {navItems.map((item, index) => (
              <TouchableOpacity
                key={item.key}
                onPress={() => handleNavigation(item.route)}
                className={`px-3 py-2 rounded-lg ${index > 0 ? 'ml-8' : ''} ${
                  activeTab === item.key
                    ? 'bg-secondary/10'
                    : 'hover:bg-gray-50'
                }`}
              >
                <Text
                  className={`font-medium ${
                    activeTab === item.key ? 'text-secondary' : 'text-gray-600'
                  }`}
                >
                  {item.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* User Profile */}
          <View className="relative" ref={dropdownRef}>
            <TouchableOpacity
              onPress={() => setShowProfileMenu(!showProfileMenu)}
              className="flex-row items-center p-2 rounded-lg"
            >
              <View className="w-8 h-8 rounded-full bg-secondary/20 items-center justify-center">
                <Text className="text-secondary font-semibold text-sm">
                  {user?.first_name?.charAt(0) || 'U'}
                </Text>
              </View>
              <Text className="text-gray-700 font-medium hidden sm:block ml-3">
                {user?.first_name || 'User'}
              </Text>
              <View className="ml-2">
                <Feather
                  name="chevron-down"
                  size={16}
                  color="#6B7280"
                  style={{
                    transform: [{ rotate: showProfileMenu ? '180deg' : '0deg' }]
                  }}
                />
              </View>
            </TouchableOpacity>

            {/* Profile Dropdown */}
            {showProfileMenu && (
              <View className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                <TouchableOpacity
                  onPress={() => {
                    setShowProfileMenu(false);
                    router.push('/(tabs)/profile');
                  }}
                  className="flex-row items-center px-4 py-2"
                >
                  <Feather name="user" size={16} color="#6B7280" />
                  <Text className="ml-3 text-gray-700">Profile Settings</Text>
                </TouchableOpacity>

                <View className="border-t border-gray-100 my-1" />

                <TouchableOpacity
                  onPress={handleLogout}
                  className="flex-row items-center px-4 py-2"
                >
                  <Feather name="log-out" size={16} color="#6B7280" />
                  <Text className="ml-3 text-gray-700">Sign Out</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  );
}
