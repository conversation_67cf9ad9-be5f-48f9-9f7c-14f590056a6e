import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Platform } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { router, usePathname } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { Logo } from '@/components/ui/Logo';

interface TopNavigationHeaderProps {
  currentTab?: string;
  showNavigation?: boolean;
}

export default function TopNavigationHeader({ currentTab, showNavigation = true }: TopNavigationHeaderProps) {
  const { state, logout } = useAuth();
  const user = state.user;
  const pathname = usePathname();
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const dropdownRef = useRef<View>(null);

  // Only show on web platform
  if (Platform.OS !== 'web') {
    return null;
  }

  // Determine if user is authenticated
  const isAuthenticated = !!user && !!state.token;

  // Close dropdown when clicking outside (web only)
  useEffect(() => {
    if (Platform.OS === 'web' && showProfileMenu) {
      const handleClickOutside = (event: any) => {
        if (dropdownRef.current && !dropdownRef.current.contains?.(event.target)) {
          setShowProfileMenu(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showProfileMenu]);

  // Navigation items
  const navItems = [
    { key: 'home', label: 'Dashboard', route: '/(tabs)/home' },
    { key: 'messages', label: 'Messages', route: '/(tabs)/messages' },
    { key: 'offers', label: 'Offers', route: '/(tabs)/offers' },
    { key: 'profile', label: 'Profile', route: '/(tabs)/profile' },
  ];

  const handleNavigation = (route: string) => {
    router.push(route as any);
  };

  const handleLogout = async () => {
    setShowProfileMenu(false);
    await logout();
  };

  const getActiveTab = () => {
    if (pathname.includes('/home')) return 'home';
    if (pathname.includes('/messages')) return 'messages';
    if (pathname.includes('/offers')) return 'offers';
    if (pathname.includes('/profile')) return 'profile';
    return 'home';
  };

  const activeTab = getActiveTab();

  return (
    <View className="bg-white border-b border-gray-200 shadow-sm">
      <View className="max-w-7xl mx-auto px-6">
        <View style={{ flexDirection: 'row', alignItems: 'center', height: 64 }}>
          {/* Left Section - Logo */}
          <View style={{ flexShrink: 0 }}>
            <Logo size="small" textClass="text-xl font-semibold text-primary" />
          </View>

          {/* Center Section - Navigation Items */}
          <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
            {isAuthenticated && showNavigation && (
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                {navItems.map((item, index) => (
                  <TouchableOpacity
                    key={item.key}
                    onPress={() => handleNavigation(item.route)}
                    className={`px-3 py-2 rounded-lg ${
                      activeTab === item.key ? 'bg-secondary/10' : 'hover:bg-gray-50'
                    }`}
                    style={{ marginLeft: index > 0 ? 32 : 0 }}
                  >
                    <Text
                      className={`font-medium ${
                        activeTab === item.key ? 'text-secondary' : 'text-gray-600'
                      }`}
                    >
                      {item.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          {/* Right Section - User Profile */}
          <View style={{ flexShrink: 0 }}>
            {isAuthenticated && showNavigation && (
              <View style={{ position: 'relative' }} ref={dropdownRef}>
                <TouchableOpacity
                  onPress={() => setShowProfileMenu(!showProfileMenu)}
                  style={{ flexDirection: 'row', alignItems: 'center', padding: 2 }}
                >
                  <View style={{ width: 32, height: 32, borderRadius: 16, backgroundColor: '#4ca2f5', alignItems: 'center', justifyContent: 'center', marginRight: 8 }}>
                    <Text style={{ color: 'white', fontSize: 14, fontWeight: '600' }}>
                      {user?.first_name?.charAt(0).toUpperCase() || 'U'}
                    </Text>
                  </View>
                  <Feather
                    name="chevron-down"
                    size={16}
                    color="#4b5563"
                    style={{ transform: [{ rotate: showProfileMenu ? '180deg' : '0deg' }] }}
                  />
                </TouchableOpacity>

                {showProfileMenu && (
                  <View className="absolute right-0 top-full mt-2 w-60 bg-white rounded-xl shadow-xl border border-gray-200 z-50">
                    <View className="px-4 py-3 border-b border-gray-200">
                      <View className="flex-row items-center mb-1">
                        <View className="w-10 h-10 rounded-full bg-secondary items-center justify-center mr-3">
                          <Text className="text-white text-base font-semibold">
                            {user?.first_name?.charAt(0).toUpperCase() || 'U'}
                          </Text>
                        </View>
                        <View className="flex-1">
                          <Text className="text-sm font-medium text-gray-800" numberOfLines={1}>
                            {`${user?.first_name || ''} ${user?.last_name || ''}`.trim() || 'User'}
                          </Text>
                          <Text className="text-xs text-gray-500" numberOfLines={1}>
                            {user?.email || '-'}
                          </Text>
                        </View>
                      </View>
                    </View>

                    <View className="py-2">
                      <TouchableOpacity
                        onPress={() => {
                          setShowProfileMenu(false);
                          router.push('/(tabs)/profile');
                        }}
                        className="flex-row items-center py-2.5 px-4 hover:bg-gray-100 active:bg-gray-200"
                      >
                        <Feather name="user" size={16} color="#4b5563" />
                        <Text className="ml-3 text-sm text-gray-700">Profile</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        onPress={handleLogout}
                        className="flex-row items-center py-2.5 px-4 hover:bg-red-50 active:bg-red-100"
                      >
                        <Feather name="log-out" size={16} color="#ef4444" />
                        <Text className="ml-3 text-sm text-red-600">Sign Out</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  );
}
