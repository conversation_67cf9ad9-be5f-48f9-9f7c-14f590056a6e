import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import { getFullApiUrl } from '../utils/apiUtils';
import { apiRequest, handleApiResponse } from '@/utils/apiUtils';
import { Tenant } from '@/types';

// Define types for our authentication state
export interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  user: Tenant | null;
  isLoading: boolean;
  error: string | null;
}

// Define action types
type AuthAction =
  | { type: 'LOGIN_REQUEST' }
  | { type: 'LOGIN_SUCCESS'; payload: { token: string; user: Tenant } }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'RESTORE_TOKEN'; payload: { token: string; user: Tenant } }
  | { type: 'FORGOT_PASSWORD_REQUEST' }
  | { type: 'FORGOT_PASSWORD_SUCCESS'; payload?: string } // Optional success message
  | { type: 'FORGOT_PASSWORD_FAILURE'; payload: string }
  | { type: 'RESET_PASSWORD_REQUEST' }
  | { type: 'RESET_PASSWORD_SUCCESS'; payload?: string } // Optional success message
  | { type: 'RESET_PASSWORD_FAILURE'; payload: string };

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  token: null,
  user: null,
  isLoading: true,
  error: null,
};

// Create the auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_REQUEST':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        token: action.payload.token,
        user: action.payload.user,
        isLoading: false,
        error: null,
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        token: null,
        user: null,
        isLoading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        token: null,
        user: null,
        isLoading: false,
        error: null,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    case 'RESTORE_TOKEN':
      return {
        ...state,
        isAuthenticated: true,
        token: action.payload.token,
        user: action.payload.user,
        isLoading: false,
        error: null,
      };
    case 'FORGOT_PASSWORD_REQUEST':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'FORGOT_PASSWORD_SUCCESS':
      return {
        ...state,
        isLoading: false,
        error: null,
        // Optionally, set a success message: action.payload
      };
    case 'FORGOT_PASSWORD_FAILURE':
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    case 'RESET_PASSWORD_REQUEST':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'RESET_PASSWORD_SUCCESS':
      return {
        ...state,
        isLoading: false,
        error: null,
        // Optionally, set a success message: action.payload
      };
    case 'RESET_PASSWORD_FAILURE':
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    default:
      return state;
  }
};

// Create the auth context
interface AuthContextType {
  state: AuthState;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  signup: (params: SignupParams) => Promise<void>;
  forgotPassword: (email: string) => Promise<{ success: boolean; message?: string }>;
  resetPassword: (token: string, password: string, passwordConfirmation: string) => Promise<{ success: boolean; message?: string }>;
}

interface SignupParams {
  invitationHash: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  password: string;
  passwordConfirmation: string;
  phone?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Create the auth provider
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check if the user is already authenticated when the app loads
  useEffect(() => {
    const bootstrapAsync = async () => {
      try {
        const token = await AsyncStorage.getItem('authToken');
        const userJson = await AsyncStorage.getItem('user');

        if (token && userJson) {
          const user = JSON.parse(userJson);
          dispatch({ type: 'RESTORE_TOKEN', payload: { token, user } });

          // Redirect to tabs if authenticated
          router.replace('/(tabs)');
        } else {
          dispatch({ type: 'LOGOUT' });

          // Always redirect to welcome on refresh/initial load
          router.replace('/(welcome)');
        }
      } catch (error) {
        console.error('Failed to restore authentication state:', error);
        dispatch({ type: 'LOGOUT' });
        // On error, show welcome screens
        router.replace('/(welcome)');
      }
    };

    bootstrapAsync();
  }, []);

  // Login function
  const login = async (email: string, password: string) => {
    dispatch({ type: 'LOGIN_REQUEST' });

    if (email === '<EMAIL>') {
      // Mock <NAME_EMAIL>
      const mockUser: Tenant = {
        id: 123, // Changed to number
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        phone: '07700900000',
        // Adding missing mandatory fields for Tenant type
        birth_date: '1985-07-15',
        personal_code: 'JD850715M', // Mock personal code
        language: 'en-GB',
        status: 'active', // Tenant status
        current_street_address: '123 Mockingbird Lane',
        current_city: 'Manchester',
        current_state: 'Greater Manchester', // UK counties can be used here
        current_postal_code: 'M1 1AA',
        current_country: 'GB',
        country_of_residence: 'GB',
        employment_status: 'employed',
        monthly_income: 3500,
        max_rent: 1500,
        bank: 'Mock HSBC UK',
        iban: '**********************', // Mock IBAN
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // Existing mock data continues below
        properties: [
          {
            id: 1, // Changed to number
            address_line_1: '10 Downing Street',
            address_line_2: '',
            city: 'London',
            postcode: 'SW1A 2AA',
            country: 'GB',
            property_type: 'Flat',
            rent_amount: 2500,
            rent_currency: 'GBP',
            status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            landlord_id: 'mock-landlord-id-001', // Kept as string
            tenant_id: 123, // Changed to number
            next_payment_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // Next week
            last_payment_date: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days ago
            lease_start_date: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000).toISOString(), // 100 days ago
            lease_end_date: new Date(Date.now() + 265 * 24 * 60 * 60 * 1000).toISOString(), // 265 days from now
            payment_day: 1,
            payment_method_id: 'mock-payment-method-id-001', // Kept as string
            payment_agreements: [
              {
                id: 101, // Changed to number
                property_id: 1, // Changed to number
                tenant_id: 123, // Changed to number
                status: 'active',
                rent_amount: 2500,
                rent_currency: 'GBP',
                payment_day: 1,
                start_date: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000).toISOString(),
                end_date: new Date(Date.now() + 265 * 24 * 60 * 60 * 1000).toISOString(),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                payment_setup_successful: true,
                direct_debit_mandate_id: 'mock-dd-mandate-001', // Kept as string
                next_payment_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                last_payment_status: 'paid',
                last_payment_date: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
              }
            ]
          },
          {
            id: 2, // Changed to number
            address_line_1: '221B Baker Street',
            address_line_2: '',
            city: 'London',
            postcode: 'NW1 6XE',
            country: 'GB',
            property_type: 'House',
            rent_amount: 3200,
            rent_currency: 'GBP',
            status: 'pending_payment_setup',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            landlord_id: 'mock-landlord-id-002', // Kept as string
            tenant_id: 123, // Changed to number
            next_payment_date: null,
            last_payment_date: null,
            lease_start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            lease_end_date: new Date(Date.now() + 335 * 24 * 60 * 60 * 1000).toISOString(),
            payment_day: 5,
            payment_method_id: null,
            payment_agreements: []
          }
        ],
        payment_methods: [
          {
            id: 'mock-payment-method-id-001', // Kept as string
            tenant_id: 123, // Changed to number
            type: 'direct_debit',
            details: {
              bank_name: 'Mock Barclays UK',
              account_holder_name: 'John Doe',
              account_number_ending: 'XX34',
              mandate_id: 'mock-dd-mandate-001', // Kept as string
              status: 'active'
            },
            is_default: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }
        ],
        notifications: [
          {
            id: 'mock-notification-id-001', // Kept as string
            tenant_id: 123, // Changed to number
            message: 'Your rent payment of £2500 for 10 Downing Street is due next week.',
            type: 'payment_reminder',
            is_read: false,
            created_at: new Date().toISOString(),
          },
          {
            id: 'mock-notification-id-002', // Kept as string
            tenant_id: 123, // Changed to number
            message: 'Welcome to CasaPay, John! Please set up your payment method for 221B Baker Street.',
            type: 'welcome',
            is_read: true,
            created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          }
        ],
        currency: 'GBP',
        locale: 'en-GB',
      };
      const mockToken = 'mock-auth-token-for-demo-user';

      // Store mock authentication token and user data
      AsyncStorage.setItem('authToken', mockToken);
      AsyncStorage.setItem('user', JSON.stringify(mockUser));

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { token: mockToken, user: mockUser }
      });

      // Navigate to tenant dashboard
      router.replace('/(dashboard)');
      return; // Important to return here to skip API call
    }

    try {
      // API call to tenant login endpoint using the environment variable
      const response = await fetch(getFullApiUrl('/api/v1/tenant/login'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          device_name: 'Expo Tenant App',
        }),
      });

      const data = await response.json();

      // Handle authentication errors
      handleApiResponse(data);

      if (data.error) {
        throw new Error(data.error.message || 'Login failed');
      }

      // Extract user and token from the response based on the actual API format
      const token = data.token;
      const user = data.tenant

      // Store authentication token and user data
      await AsyncStorage.setItem('authToken', token);
      await AsyncStorage.setItem('user', JSON.stringify(user));

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { token, user }
      });

      // Navigate to tenant tabs
      router.replace('/(tabs)');
    } catch (error) {
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: error instanceof Error ? error.message : 'An error occurred during login'
      });
    }
  };

  // Signup function for invitation-based registration
  const signup = async (params: SignupParams) => {
    dispatch({ type: 'LOGIN_REQUEST' });

    try {
      // API call to tenant signup endpoint
      const response = await fetch(getFullApiUrl('/api/v1/tenant/signup'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          invitation_hash: params.invitationHash,
          password: params.password,
          password_confirmation: params.passwordConfirmation,
          device_name: 'Expo Tenant App',
        }),
      });

      const data = await response.json();

      // Handle authentication errors
      handleApiResponse(data);

      if (data.errors) {
        throw new Error(data.message || 'Signup failed');
      }

      // Extract user and token from the response
      const token = data.token;
      const user = data.tenant;

      // Store authentication token and user data
      await AsyncStorage.setItem('authToken', token);
      await AsyncStorage.setItem('user', JSON.stringify(user));

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { token, user }
      });

      // Navigate to tenant dashboard
      router.replace('/(dashboard)');
    } catch (error) {
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: error instanceof Error ? error.message : 'An error occurred during signup'
      });
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Clear authentication data from storage
      await AsyncStorage.removeItem('authToken');
      await AsyncStorage.removeItem('user');

      dispatch({ type: 'LOGOUT' });

      // Navigate to welcome screen instead of login
      router.replace('/(welcome)');
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  // Clear error function
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Forgot password function
  const forgotPassword = async (email: string): Promise<{ success: boolean; message?: string }> => {
    dispatch({ type: 'FORGOT_PASSWORD_REQUEST' });
    try {
      const response = await fetch(getFullApiUrl('/api/v1/tenant/password/email'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();
      handleApiResponse(data); // Checks for errors like 422, 500 etc.

      if (!response.ok || data.error || data.errors) {
        const errorMessage = data.error?.message || data.message || (data.errors ? Object.values(data.errors).flat().join(', ') : 'Failed to send reset instructions.');
        throw new Error(errorMessage);
      }

      dispatch({ type: 'FORGOT_PASSWORD_SUCCESS', payload: data.message });
      // The component calling this can then show data.message or a generic success message.
      return { success: true, message: data.message };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An unknown error occurred.';
      dispatch({ type: 'FORGOT_PASSWORD_FAILURE', payload: message });
      return { success: false, message };
    }
  };

  // Reset password function
  const resetPassword = async (token: string, password: string, passwordConfirmation: string): Promise<{ success: boolean; message?: string }> => {
    dispatch({ type: 'RESET_PASSWORD_REQUEST' });
    try {
      const response = await fetch(getFullApiUrl('/api/v1/tenant/password/reset'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          token,
          password,
          password_confirmation: passwordConfirmation,
          // email might be required by your backend, or it might get it from the token
        }),
      });

      const data = await response.json();
      handleApiResponse(data); // Checks for errors

      if (!response.ok || data.error || data.errors) {
         const errorMessage = data.error?.message || data.message || (data.errors ? Object.values(data.errors).flat().join(', ') : 'Failed to reset password.');
        throw new Error(errorMessage);
      }

      dispatch({ type: 'RESET_PASSWORD_SUCCESS', payload: data.message });
      // The component calling this can then show data.message or a generic success message, and navigate to login.
      return { success: true, message: data.message };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An unknown error occurred.';
      dispatch({ type: 'RESET_PASSWORD_FAILURE', payload: message });
      return { success: false, message };
    }
  };

  // Provide the auth context
  return (
    <AuthContext.Provider
      value={{
        state,
        login,
        logout,
        clearError,
        signup,
        forgotPassword,
        resetPassword,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Create a hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};
